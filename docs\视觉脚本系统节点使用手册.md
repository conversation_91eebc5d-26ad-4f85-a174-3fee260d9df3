# DL引擎视觉脚本系统节点使用手册

## 目录
1. [系统概述](#系统概述)
2. [节点分类](#节点分类)
3. [核心基础节点](#核心基础节点)
4. [实体与组件节点](#实体与组件节点)
5. [物理系统节点](#物理系统节点)
6. [渲染与视觉效果节点](#渲染与视觉效果节点)
7. [动画系统节点](#动画系统节点)
8. [音频系统节点](#音频系统节点)
9. [输入系统节点](#输入系统节点)
10. [网络通信节点](#网络通信节点)
11. [用户界面节点](#用户界面节点)
12. [专业系统节点](#专业系统节点)
13. [节点使用最佳实践](#节点使用最佳实践)

## 系统概述

DL引擎的视觉脚本系统是一个强大的无代码编程环境，包含413个专业节点，覆盖了3D应用开发的所有方面。每个节点都经过精心设计，具有清晰的输入输出接口、详细的参数配置和丰富的应用场景。

### 核心特性
- **无代码编程**：通过拖拽连接节点实现复杂逻辑
- **实时执行**：节点修改即时生效，支持热重载
- **类型安全**：强类型系统确保连接正确性
- **性能优化**：内置性能分析和优化建议
- **扩展性强**：支持自定义节点开发

### 节点结构
每个节点包含以下基本元素：
- **输入端口**：接收数据的连接点
- **输出端口**：输出数据的连接点
- **参数面板**：节点配置选项
- **执行流**：控制节点执行顺序
- **帮助信息**：节点使用说明和示例

## 节点分类

### 按功能分类
1. **核心基础节点** (53个)：流程控制、数据操作、数学运算
2. **实体与组件节点** (45个)：对象管理、组件操作、变换控制
3. **物理系统节点** (28个)：刚体物理、软体物理、碰撞检测
4. **渲染与视觉效果节点** (67个)：材质、光照、相机、后处理
5. **动画系统节点** (35个)：关键帧动画、骨骼动画、状态机
6. **音频系统节点** (25个)：音频播放、3D音效、音频处理
7. **输入系统节点** (32个)：键鼠输入、触摸输入、VR输入
8. **网络通信节点** (45个)：WebSocket、WebRTC、HTTP请求
9. **用户界面节点** (34个)：UI组件、布局管理、事件处理
10. **专业系统节点** (47个)：区块链、学习记录、空间信息

### 按复杂度分类
- **初级节点**：基础操作，适合新手学习
- **中级节点**：常用功能，日常开发必备
- **高级节点**：专业功能，特定场景使用
- **专家节点**：复杂系统，需要深度理解

## 核心基础节点

### 1. 核心节点 (14个)

#### OnStartNode - 开始事件节点
**功能描述**：场景启动时触发的事件节点，是脚本执行的入口点。

**节点原理**：
- 在场景加载完成后自动触发
- 只执行一次，适合初始化操作
- 是所有脚本逻辑的起始点

**输入端口**：无

**输出端口**：
- `Execute` (执行流)：触发后续节点执行

**参数配置**：
- `Delay` (延迟时间)：延迟执行时间，单位秒，默认0
- `Priority` (优先级)：执行优先级，数值越大越先执行

**使用方法**：
1. 将OnStartNode拖拽到画布
2. 连接Execute输出到需要执行的节点
3. 设置延迟时间和优先级（可选）

**应用场景**：
- 初始化游戏状态
- 加载必要资源
- 设置初始UI界面
- 播放开场动画

**示例代码**：
```
OnStart → SetVariable(playerHealth, 100) → CreateUI(healthBar)
```

#### BranchNode - 条件分支节点
**功能描述**：根据条件判断选择不同的执行路径。

**节点原理**：
- 接收布尔值条件输入
- 根据条件真假选择不同输出
- 支持复杂的逻辑判断

**输入端口**：
- `Execute` (执行流)：触发节点执行
- `Condition` (条件)：布尔值，判断条件

**输出端口**：
- `True` (真分支)：条件为真时执行
- `False` (假分支)：条件为假时执行

**参数配置**：
- `InvertCondition` (反转条件)：是否反转判断结果

**使用方法**：
1. 连接执行流到Execute输入
2. 连接条件表达式到Condition输入
3. 分别连接True和False输出到对应逻辑

**应用场景**：
- 游戏状态判断
- 用户权限检查
- 资源加载状态判断
- 设备兼容性检测

**示例代码**：
```
GetVariable(playerHealth) → Compare(>, 0) → Branch
├─ True → ContinueGame
└─ False → GameOver
```

#### ForLoopNode - 循环节点
**功能描述**：按指定次数重复执行一段逻辑。

**节点原理**：
- 设置循环起始值、结束值和步长
- 每次循环输出当前索引值
- 支持嵌套循环和提前退出

**输入端口**：
- `Execute` (执行流)：开始循环
- `StartIndex` (起始索引)：循环起始值
- `EndIndex` (结束索引)：循环结束值
- `Step` (步长)：每次递增值

**输出端口**：
- `LoopBody` (循环体)：每次循环执行
- `Index` (当前索引)：当前循环索引值
- `Completed` (完成)：循环结束后执行

**参数配置**：
- `DefaultStart` (默认起始值)：默认0
- `DefaultEnd` (默认结束值)：默认10
- `DefaultStep` (默认步长)：默认1

**使用方法**：
1. 设置循环参数或连接动态输入
2. 连接循环体逻辑到LoopBody输出
3. 使用Index输出获取当前循环次数

**应用场景**：
- 批量创建对象
- 数组遍历处理
- 重复动画播放
- 数据批量处理

**示例代码**：
```
ForLoop(0, 10, 1)
├─ LoopBody → CreateCube → SetPosition(Index * 2, 0, 0)
└─ Completed → ShowMessage("创建完成")
```

#### SetVariableNode - 设置变量节点
**功能描述**：存储数据到变量中，供其他节点使用。

**节点原理**：
- 支持多种数据类型存储
- 变量具有作用域概念
- 支持全局和局部变量

**输入端口**：
- `Execute` (执行流)：触发设置操作
- `Value` (值)：要存储的数据

**输出端口**：
- `Execute` (执行流)：设置完成后继续执行

**参数配置**：
- `VariableName` (变量名)：变量标识符
- `VariableType` (变量类型)：数据类型
- `Scope` (作用域)：Global/Local/Scene
- `Persistent` (持久化)：是否保存到本地

**使用方法**：
1. 设置变量名和类型
2. 连接数据源到Value输入
3. 连接执行流触发设置操作

**应用场景**：
- 游戏状态保存
- 用户设置存储
- 临时数据缓存
- 跨场景数据传递

#### GetVariableNode - 获取变量节点
**功能描述**：从变量中读取存储的数据。

**节点原理**：
- 根据变量名查找对应数据
- 支持类型转换和默认值
- 实时监听变量变化

**输入端口**：无

**输出端口**：
- `Value` (值)：变量存储的数据

**参数配置**：
- `VariableName` (变量名)：要读取的变量
- `DefaultValue` (默认值)：变量不存在时的默认值
- `AutoUpdate` (自动更新)：变量变化时自动更新输出

**使用方法**：
1. 设置要读取的变量名
2. 连接输出到需要使用数据的节点
3. 设置默认值防止错误

**应用场景**：
- 读取游戏状态
- 获取用户设置
- 访问共享数据
- 条件判断数据源

### 2. 数学节点 (16个)

#### AddNode - 加法节点
**功能描述**：计算两个或多个数值的和。

**节点原理**：
- 支持整数和浮点数运算
- 支持向量和矩阵加法
- 自动类型转换和精度处理

**输入端口**：
- `A` (数值A)：第一个加数
- `B` (数值B)：第二个加数
- `...` (更多输入)：支持多个输入

**输出端口**：
- `Result` (结果)：计算结果

**参数配置**：
- `InputCount` (输入数量)：输入端口数量，2-10
- `Precision` (精度)：小数点后保留位数

**使用方法**：
1. 连接数值到输入端口
2. 设置输入数量和精度
3. 使用Result输出获取计算结果

**应用场景**：
- 分数累加计算
- 位置偏移计算
- 数值统计汇总
- 物理力的合成

#### MultiplyNode - 乘法节点
**功能描述**：计算两个数值的乘积。

**节点原理**：
- 支持标量乘法运算
- 支持向量缩放操作
- 支持矩阵变换计算

**输入端口**：
- `A` (数值A)：被乘数
- `B` (数值B)：乘数

**输出端口**：
- `Result` (结果)：乘积结果

**参数配置**：
- `ClampResult` (限制结果)：是否限制结果范围
- `MinValue` (最小值)：结果最小值
- `MaxValue` (最大值)：结果最大值

**使用方法**：
1. 连接两个数值到输入端口
2. 设置结果限制范围（可选）
3. 使用Result输出获取乘积

**应用场景**：
- 伤害计算（基础伤害 × 倍率）
- 缩放变换（尺寸 × 缩放因子）
- 速度计算（时间 × 加速度）
- 经济系统（价格 × 数量）

#### VectorMathNode - 向量数学节点
**功能描述**：执行3D向量的各种数学运算。

**节点原理**：
- 支持向量加减乘除运算
- 提供点积、叉积、归一化等操作
- 包含距离、角度、投影计算

**输入端口**：
- `VectorA` (向量A)：第一个向量
- `VectorB` (向量B)：第二个向量（部分操作需要）

**输出端口**：
- `Result` (结果)：运算结果（向量或标量）

**参数配置**：
- `Operation` (操作类型)：
  - Add (加法)
  - Subtract (减法)
  - Multiply (乘法)
  - Divide (除法)
  - DotProduct (点积)
  - CrossProduct (叉积)
  - Normalize (归一化)
  - Distance (距离)
  - Angle (角度)
  - Project (投影)

**使用方法**：
1. 选择要执行的向量操作
2. 连接向量数据到输入端口
3. 根据操作类型使用相应输出

**应用场景**：
- 物体朝向计算
- 距离检测判断
- 力的方向计算
- 相机视角控制

### 3. 逻辑节点 (8个)

#### CompareNode - 比较节点
**功能描述**：比较两个值的大小关系。

**节点原理**：
- 支持数值、字符串、布尔值比较
- 提供多种比较操作符
- 输出布尔值结果

**输入端口**：
- `A` (值A)：第一个比较值
- `B` (值B)：第二个比较值

**输出端口**：
- `Result` (结果)：比较结果（布尔值）

**参数配置**：
- `Operator` (操作符)：
  - Equal (等于)
  - NotEqual (不等于)
  - Greater (大于)
  - GreaterEqual (大于等于)
  - Less (小于)
  - LessEqual (小于等于)
- `Tolerance` (容差)：浮点数比较的误差范围

**使用方法**：
1. 选择比较操作符
2. 连接要比较的值到输入端口
3. 使用Result输出进行条件判断

**应用场景**：
- 生命值检查
- 分数排名判断
- 距离阈值检测
- 时间条件判断

#### AndNode - 逻辑与节点
**功能描述**：执行逻辑与运算，所有输入都为真时输出真。

**节点原理**：
- 接收多个布尔值输入
- 只有全部为真时输出真
- 支持短路求值优化

**输入端口**：
- `Input1` (输入1)：第一个布尔值
- `Input2` (输入2)：第二个布尔值
- `...` (更多输入)：支持多个输入

**输出端口**：
- `Result` (结果)：逻辑与结果

**参数配置**：
- `InputCount` (输入数量)：输入端口数量，2-8

**使用方法**：
1. 设置输入数量
2. 连接布尔值到各个输入端口
3. 使用Result输出获取逻辑与结果

**应用场景**：
- 多条件权限检查
- 复合状态判断
- 安全验证逻辑
- 游戏规则验证

#### OrNode - 逻辑或节点
**功能描述**：执行逻辑或运算，任一输入为真时输出真。

**节点原理**：
- 接收多个布尔值输入
- 任意一个为真时输出真
- 支持短路求值优化

**输入端口**：
- `Input1` (输入1)：第一个布尔值
- `Input2` (输入2)：第二个布尔值
- `...` (更多输入)：支持多个输入

**输出端口**：
- `Result` (结果)：逻辑或结果

**参数配置**：
- `InputCount` (输入数量)：输入端口数量，2-8

**使用方法**：
1. 设置输入数量
2. 连接布尔值到各个输入端口
3. 使用Result输出获取逻辑或结果

**应用场景**：
- 多种触发条件
- 备选方案选择
- 异常情况处理
- 用户输入检测

### 4. 字符串节点 (7个)

#### ConcatenateNode - 字符串连接节点
**功能描述**：将多个字符串连接成一个字符串。

**节点原理**：
- 按顺序连接输入的字符串
- 支持自定义分隔符
- 自动处理空值和类型转换

**输入端口**：
- `String1` (字符串1)：第一个字符串
- `String2` (字符串2)：第二个字符串
- `...` (更多输入)：支持多个输入

**输出端口**：
- `Result` (结果)：连接后的字符串

**参数配置**：
- `InputCount` (输入数量)：输入端口数量，2-10
- `Separator` (分隔符)：连接时使用的分隔符
- `IgnoreEmpty` (忽略空值)：是否忽略空字符串

**使用方法**：
1. 设置输入数量和分隔符
2. 连接字符串到各个输入端口
3. 使用Result输出获取连接结果

**应用场景**：
- 动态文本生成
- 文件路径构建
- 用户信息显示
- 日志消息组装

#### FormatStringNode - 字符串格式化节点
**功能描述**：使用模板格式化字符串，支持参数替换。

**节点原理**：
- 使用占位符模板
- 支持多种数据类型格式化
- 提供丰富的格式化选项

**输入端口**：
- `Template` (模板)：格式化模板字符串
- `Param1` (参数1)：第一个替换参数
- `Param2` (参数2)：第二个替换参数
- `...` (更多参数)：支持多个参数

**输出端口**：
- `Result` (结果)：格式化后的字符串

**参数配置**：
- `ParameterCount` (参数数量)：参数端口数量
- `DateFormat` (日期格式)：日期类型的格式化样式
- `NumberFormat` (数字格式)：数字类型的格式化样式

**使用方法**：
1. 设置模板字符串，使用{0}, {1}等占位符
2. 连接参数到对应输入端口
3. 使用Result输出获取格式化结果

**应用场景**：
- 游戏UI文本显示
- 分数和时间格式化
- 多语言文本处理
- 报告和日志生成

**示例模板**：
```
"玩家 {0} 获得了 {1} 分，当前排名第 {2} 位"
"剩余时间：{0:mm:ss}"
"金币数量：{0:N0}"
```

### 5. 数组节点 (8个)

#### ArrayGetNode - 数组获取节点
**功能描述**：从数组中获取指定索引位置的元素。

**节点原理**：
- 支持正向和反向索引访问
- 提供边界检查和安全访问
- 支持多维数组访问

**输入端口**：
- `Array` (数组)：目标数组
- `Index` (索引)：元素索引位置

**输出端口**：
- `Element` (元素)：获取的数组元素
- `IsValid` (有效性)：索引是否有效

**参数配置**：
- `DefaultValue` (默认值)：索引无效时返回的默认值
- `AllowNegativeIndex` (允许负索引)：是否支持负数索引（从末尾开始）
- `ClampIndex` (限制索引)：是否将索引限制在有效范围内

**使用方法**：
1. 连接数组数据到Array输入
2. 连接或设置索引值
3. 使用Element输出获取元素，IsValid检查有效性

**应用场景**：
- 获取排行榜特定位置玩家
- 访问库存物品
- 读取关卡配置数据
- 获取动画关键帧

#### ArraySetNode - 数组设置节点
**功能描述**：设置数组中指定索引位置的元素值。

**节点原理**：
- 修改数组中指定位置的元素
- 支持数组自动扩展
- 提供类型安全检查

**输入端口**：
- `Execute` (执行流)：触发设置操作
- `Array` (数组)：目标数组
- `Index` (索引)：设置位置
- `Value` (值)：要设置的值

**输出端口**：
- `Execute` (执行流)：设置完成后继续
- `ModifiedArray` (修改后数组)：更新后的数组

**参数配置**：
- `AutoExpand` (自动扩展)：索引超出范围时是否自动扩展数组
- `FillValue` (填充值)：扩展时使用的填充值

**使用方法**：
1. 连接执行流触发操作
2. 连接数组、索引和新值
3. 使用ModifiedArray获取更新后的数组

**应用场景**：
- 更新玩家库存
- 修改关卡进度
- 设置配置参数
- 更新排行榜数据

#### ArrayAddNode - 数组添加节点
**功能描述**：向数组末尾添加新元素。

**节点原理**：
- 在数组末尾追加元素
- 自动处理数组扩容
- 支持批量添加操作

**输入端口**：
- `Execute` (执行流)：触发添加操作
- `Array` (数组)：目标数组
- `Element` (元素)：要添加的元素

**输出端口**：
- `Execute` (执行流)：添加完成后继续
- `ModifiedArray` (修改后数组)：更新后的数组
- `NewIndex` (新索引)：新元素的索引位置

**参数配置**：
- `MaxSize` (最大大小)：数组最大容量限制
- `OverflowBehavior` (溢出行为)：超出最大容量时的处理方式

**使用方法**：
1. 连接执行流和目标数组
2. 连接要添加的元素
3. 使用输出获取更新结果

**应用场景**：
- 添加新获得的物品
- 记录玩家操作历史
- 收集游戏统计数据
- 构建动态列表

## 实体与组件节点

### 1. 实体节点 (15个)

#### CreateEntityNode - 创建实体节点
**功能描述**：在场景中创建新的游戏实体对象。

**节点原理**：
- 基于ECS架构创建实体
- 支持从预制体实例化
- 自动分配唯一ID和管理生命周期

**输入端口**：
- `Execute` (执行流)：触发创建操作
- `Name` (名称)：实体名称
- `Position` (位置)：初始位置
- `Rotation` (旋转)：初始旋转
- `Scale` (缩放)：初始缩放
- `Parent` (父对象)：父实体引用

**输出端口**：
- `Execute` (执行流)：创建完成后继续
- `Entity` (实体)：创建的实体引用
- `EntityID` (实体ID)：实体唯一标识符

**参数配置**：
- `Prefab` (预制体)：基础预制体模板
- `AutoActivate` (自动激活)：创建后是否立即激活
- `DestroyOnSceneChange` (场景切换时销毁)：是否随场景销毁

**使用方法**：
1. 选择预制体模板（可选）
2. 设置初始变换参数
3. 连接执行流触发创建
4. 使用Entity输出引用新创建的对象

**应用场景**：
- 动态生成敌人
- 创建特效对象
- 实例化UI元素
- 生成环境装饰

**示例流程**：
```
OnPlayerDeath → CreateEntity(GhostPrefab) → SetPosition(PlayerLastPosition) → PlayAnimation(FadeIn)
```

#### DestroyEntityNode - 销毁实体节点
**功能描述**：从场景中移除并销毁指定的实体对象。

**节点原理**：
- 安全地移除实体及其所有组件
- 自动处理子对象和引用清理
- 支持延迟销毁和销毁特效

**输入端口**：
- `Execute` (执行流)：触发销毁操作
- `Entity` (实体)：要销毁的实体引用
- `Delay` (延迟)：延迟销毁时间（秒）

**输出端口**：
- `Execute` (执行流)：销毁完成后继续
- `WasDestroyed` (已销毁)：是否成功销毁

**参数配置**：
- `DestroyChildren` (销毁子对象)：是否同时销毁所有子对象
- `PlayDestroyEffect` (播放销毁特效)：销毁时播放的特效
- `NotifyOthers` (通知其他对象)：是否发送销毁事件通知

**使用方法**：
1. 连接要销毁的实体引用
2. 设置延迟时间（可选）
3. 配置销毁行为参数
4. 连接执行流触发销毁

**应用场景**：
- 清理过期对象
- 移除被击败的敌人
- 销毁临时特效
- 清理内存资源

#### FindEntityNode - 查找实体节点
**功能描述**：根据条件在场景中查找特定的实体对象。

**节点原理**：
- 支持多种查找条件和方式
- 提供高效的查找算法
- 支持模糊匹配和正则表达式

**输入端口**：
- `SearchCriteria` (搜索条件)：查找条件字符串

**输出端口**：
- `Entity` (实体)：找到的实体引用
- `Entities` (实体列表)：所有匹配的实体
- `Found` (已找到)：是否找到匹配实体

**参数配置**：
- `SearchType` (搜索类型)：
  - ByName (按名称)
  - ByTag (按标签)
  - ByComponent (按组件)
  - ByDistance (按距离)
- `SearchScope` (搜索范围)：
  - Scene (整个场景)
  - Children (子对象)
  - Parent (父对象)
- `MaxResults` (最大结果数)：返回结果的最大数量

**使用方法**：
1. 选择搜索类型和范围
2. 设置搜索条件
3. 使用输出获取查找结果

**应用场景**：
- 查找特定NPC
- 获取最近的敌人
- 找到UI元素
- 定位关键对象

### 2. 组件节点 (15个)

#### AddComponentNode - 添加组件节点
**功能描述**：为实体添加新的组件，扩展其功能。

**节点原理**：
- 基于ECS架构动态添加组件
- 支持组件参数初始化
- 自动处理组件依赖关系

**输入端口**：
- `Execute` (执行流)：触发添加操作
- `Entity` (实体)：目标实体引用
- `ComponentData` (组件数据)：组件初始化数据

**输出端口**：
- `Execute` (执行流)：添加完成后继续
- `Component` (组件)：新添加的组件引用
- `Success` (成功)：是否成功添加

**参数配置**：
- `ComponentType` (组件类型)：要添加的组件类型
- `InitializeData` (初始化数据)：组件初始参数
- `ReplaceExisting` (替换现有)：如果组件已存在是否替换

**使用方法**：
1. 选择要添加的组件类型
2. 连接目标实体引用
3. 设置组件初始化参数
4. 连接执行流触发添加

**应用场景**：
- 动态添加物理组件
- 为对象添加AI行为
- 增加视觉效果组件
- 添加交互功能

#### GetComponentNode - 获取组件节点
**功能描述**：从实体中获取指定类型的组件引用。

**节点原理**：
- 根据组件类型查找组件
- 支持继承关系查找
- 提供类型安全的组件访问

**输入端口**：
- `Entity` (实体)：目标实体引用

**输出端口**：
- `Component` (组件)：找到的组件引用
- `HasComponent` (有组件)：实体是否包含该组件

**参数配置**：
- `ComponentType` (组件类型)：要获取的组件类型
- `SearchInChildren` (在子对象中搜索)：是否在子对象中查找
- `SearchInParent` (在父对象中搜索)：是否在父对象中查找

**使用方法**：
1. 选择要获取的组件类型
2. 连接实体引用
3. 使用Component输出访问组件
4. 使用HasComponent检查组件存在性

**应用场景**：
- 访问物理组件进行力的施加
- 获取渲染组件修改材质
- 访问动画组件控制播放
- 获取脚本组件调用方法

### 3. 变换节点 (15个)

#### SetPositionNode - 设置位置节点
**功能描述**：设置实体在3D空间中的位置坐标。

**节点原理**：
- 直接修改实体的Transform组件
- 支持世界坐标和本地坐标
- 提供平滑移动和瞬间移动选项

**输入端口**：
- `Execute` (执行流)：触发设置操作
- `Entity` (实体)：目标实体引用
- `Position` (位置)：新的位置坐标
- `Duration` (持续时间)：移动动画时间

**输出端口**：
- `Execute` (执行流)：设置完成后继续
- `FinalPosition` (最终位置)：实际设置的位置

**参数配置**：
- `CoordinateSpace` (坐标空间)：World/Local
- `AnimationType` (动画类型)：Instant/Linear/Smooth
- `EaseType` (缓动类型)：动画缓动曲线

**使用方法**：
1. 连接目标实体和新位置
2. 选择坐标空间和动画类型
3. 设置移动持续时间（如需动画）
4. 连接执行流触发移动

**应用场景**：
- 传送玩家到指定位置
- 移动平台和电梯
- 物品掉落位置设置
- 相机位置调整

#### GetPositionNode - 获取位置节点
**功能描述**：获取实体当前在3D空间中的位置坐标。

**节点原理**：
- 读取实体Transform组件的位置信息
- 支持世界坐标和本地坐标获取
- 实时更新位置数据

**输入端口**：
- `Entity` (实体)：目标实体引用

**输出端口**：
- `Position` (位置)：当前位置坐标
- `X` (X坐标)：X轴坐标值
- `Y` (Y坐标)：Y轴坐标值
- `Z` (Z坐标)：Z轴坐标值

**参数配置**：
- `CoordinateSpace` (坐标空间)：World/Local
- `UpdateFrequency` (更新频率)：位置更新频率

**使用方法**：
1. 连接要查询的实体引用
2. 选择坐标空间类型
3. 使用Position输出获取完整坐标
4. 使用X/Y/Z输出获取单轴坐标

**应用场景**：
- 距离计算和检测
- 位置信息显示
- 路径规划和导航
- 碰撞检测判断

#### MoveNode - 移动节点
**功能描述**：按指定方向和距离移动实体。

**节点原理**：
- 基于当前位置进行相对移动
- 支持多种移动模式和速度控制
- 提供碰撞检测和路径规划

**输入端口**：
- `Execute` (执行流)：触发移动操作
- `Entity` (实体)：要移动的实体
- `Direction` (方向)：移动方向向量
- `Distance` (距离)：移动距离
- `Speed` (速度)：移动速度

**输出端口**：
- `Execute` (执行流)：移动完成后继续
- `NewPosition` (新位置)：移动后的位置
- `ActualDistance` (实际距离)：实际移动的距离

**参数配置**：
- `MoveMode` (移动模式)：
  - Instant (瞬间移动)
  - Linear (线性移动)
  - Physics (物理移动)
- `CollisionDetection` (碰撞检测)：是否检测移动路径上的碰撞
- `StopOnCollision` (碰撞时停止)：遇到碰撞是否停止移动

**使用方法**：
1. 连接要移动的实体
2. 设置移动方向和距离
3. 选择移动模式和速度
4. 连接执行流开始移动

**应用场景**：
- 角色移动控制
- 物体推拉操作
- 平台移动机制
- 弹射物轨迹

## 物理系统节点

### 1. 刚体物理节点 (15个)

#### AddRigidBodyNode - 添加刚体节点
**功能描述**：为实体添加刚体物理组件，使其参与物理模拟。

**节点原理**：
- 基于物理引擎创建刚体对象
- 支持静态、动态、运动学三种类型
- 自动计算质量和惯性参数

**输入端口**：
- `Execute` (执行流)：触发添加操作
- `Entity` (实体)：目标实体引用
- `Mass` (质量)：刚体质量
- `Drag` (阻力)：线性阻力系数
- `AngularDrag` (角阻力)：角速度阻力系数

**输出端口**：
- `Execute` (执行流)：添加完成后继续
- `RigidBody` (刚体)：创建的刚体组件引用

**参数配置**：
- `BodyType` (刚体类型)：
  - Dynamic (动态)：受力影响，可移动
  - Static (静态)：不移动，不受力影响
  - Kinematic (运动学)：可移动，不受力影响
- `Material` (物理材质)：摩擦力和弹性参数
- `FreezeRotation` (冻结旋转)：是否锁定旋转轴

**使用方法**：
1. 选择刚体类型和物理材质
2. 连接目标实体引用
3. 设置质量和阻力参数
4. 连接执行流添加刚体

**应用场景**：
- 可推拉的箱子
- 掉落的物品
- 可破坏的环境
- 载具物理模拟

#### ApplyForceNode - 施加力节点
**功能描述**：对刚体施加力或冲量，产生物理运动效果。

**节点原理**：
- 基于牛顿第二定律施加力
- 支持不同的力施加模式
- 考虑质量对加速度的影响

**输入端口**：
- `Execute` (执行流)：触发施力操作
- `RigidBody` (刚体)：目标刚体引用
- `Force` (力)：施加的力向量
- `Position` (位置)：力的作用点（可选）

**输出端口**：
- `Execute` (执行流)：施力完成后继续

**参数配置**：
- `ForceMode` (力模式)：
  - Force (持续力)：考虑质量和时间
  - Impulse (冲量)：瞬间力，考虑质量
  - Acceleration (加速度)：不考虑质量
  - VelocityChange (速度变化)：直接改变速度
- `RelativeToBody` (相对于刚体)：力方向是否相对于刚体朝向

**使用方法**：
1. 连接目标刚体引用
2. 设置力的大小和方向
3. 选择合适的力模式
4. 连接执行流施加力

**应用场景**：
- 爆炸冲击波效果
- 角色跳跃机制
- 风力环境效果
- 载具推进力

#### CollisionDetectionNode - 碰撞检测节点
**功能描述**：检测和响应物体之间的碰撞事件。

**节点原理**：
- 监听物理引擎的碰撞事件
- 提供碰撞信息和接触点数据
- 支持碰撞过滤和分层

**输入端口**：
- `Entity` (实体)：要监听碰撞的实体

**输出端口**：
- `OnCollisionEnter` (碰撞开始)：开始碰撞时触发
- `OnCollisionStay` (碰撞持续)：碰撞持续时触发
- `OnCollisionExit` (碰撞结束)：碰撞结束时触发
- `OtherEntity` (碰撞对象)：碰撞的另一个实体
- `ContactPoint` (接触点)：碰撞接触点位置
- `ContactNormal` (接触法线)：碰撞表面法线方向
- `ImpactForce` (冲击力)：碰撞冲击力大小

**参数配置**：
- `CollisionLayers` (碰撞层)：监听的碰撞层级
- `MinImpactForce` (最小冲击力)：触发事件的最小冲击力
- `ContinuousDetection` (连续检测)：是否启用连续碰撞检测

**使用方法**：
1. 连接要监听的实体
2. 设置碰撞层和过滤条件
3. 连接相应的输出事件到处理逻辑
4. 使用碰撞信息进行响应处理

**应用场景**：
- 伤害判定系统
- 拾取物品检测
- 触发器机制
- 音效播放触发

### 2. 软体物理节点 (13个)

#### ClothSystemNode - 布料系统节点
**功能描述**：创建和控制布料物理模拟效果。

**节点原理**：
- 基于质点弹簧模型模拟布料
- 支持风力、重力等外力影响
- 提供撕裂和约束功能

**输入端口**：
- `Execute` (执行流)：触发创建操作
- `Mesh` (网格)：布料网格模型
- `Density` (密度)：布料密度
- `Stiffness` (刚度)：布料刚度系数
- `Damping` (阻尼)：运动阻尼系数

**输出端口**：
- `Execute` (执行流)：创建完成后继续
- `ClothComponent` (布料组件)：创建的布料组件

**参数配置**：
- `Resolution` (分辨率)：模拟精度
- `WindEffect` (风力效果)：是否受风力影响
- `SelfCollision` (自碰撞)：是否启用自身碰撞
- `TearingEnabled` (启用撕裂)：是否允许布料撕裂

**使用方法**：
1. 准备布料网格模型
2. 设置物理参数
3. 配置模拟选项
4. 连接执行流创建布料

**应用场景**：
- 角色服装模拟
- 窗帘和旗帜效果
- 帐篷和遮阳棚
- 装饰性布料元素

---

*由于节点数量庞大（413个），本手册将持续更新和完善。每个节点都包含详细的使用说明、参数配置和应用示例。*
